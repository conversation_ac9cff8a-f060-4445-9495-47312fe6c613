#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <string.h> // for sprintf
#include <complex.h> // for complex numbers

// グローバル変数として #define を使用
#define N_MODES (100) // フーリエモードの最大数 (Nに相当)
#define NT (300)      // 時間ステップ数
#define NX (300)      // 空間グリッド点数
#define T_END (5.0)   // シミュレーション終了時間
#define C_VEL (0.5)   // 移流速度 c

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

int main() {
    int i, n, k;
    double dt, dx;
    double x[NX + 1];
    double u_real[NX + 1]; // 物理空間でのu(x,t)
    
    // フーリエ係数
    double ak_n[N_MODES + 1], bk_n[N_MODES + 1]; // 時刻 n の係数
    double ak_np1[N_MODES + 1], bk_np1[N_MODES + 1]; // 時刻 n+1 の係数

    char fname[64]; // ファイル名バッファ
    FILE *fp_data, *fp_gnuplot;

    dt = T_END / NT; // 時間刻み幅
    dx = (2.0 * M_PI) / NX; // 空間刻み幅 (0から2πの範囲)

    // 空間グリッドの初期化
    for (i = 0; i <= NX; i++) {
        x[i] = i * dx;
    

    // 初期条件のフーリエ係数 ak(0), bk(0) を計算
    // u(x,0) = exp(x)
        for (k = 0; k <= N_MODES; k++) {

        double exp_2pi_minus_1 = exp(2.0 * M_PI) - 1.0;
        double denom = M_PI * (1.0 + k * k);      
        ak_n[k] = exp_2pi_minus_1 / denom;
        bk_n[k] = -k * exp_2pi_minus_1 / denom;

            // k=0 の場合、b_0(0) = 0
            if (k == 0) {
                bk_n[k] = 0.0;
            }
    }}

    // 負のモードの扱いは a_{-k} = a_k, b_{-k} = -b_k (u(x,t)が実数の場合)
    // ただし、計算は k >= 0 の範囲で行い、u(x,t)の再構成時に対応する。

    // タイムステップループ
    for (n = 0; n <= NT; n++) { // NT+1個のファイルを出力するため <=NT

        // 物理空間の u(x,t) を再構成
        for (i = 0; i <= NX; i++) {
            u_real[i] = ak_n[0]; // k=0 の項 (a0(t)のみ)
            for (k = 1; k <= N_MODES; k++) {
                // u(x,t) = a0 + sum_{k=1}^N (ak cos(kx) + bk sin(kx))
                // (a_k - i b_k) e^{ikx} + (a_k + i b_k) e^{-ikx} を展開すると
                // 2 * (a_k cos(kx) + b_k sin(kx)) となることに注意 (実数のフーリエ級数展開の場合)
                // 定義に依存するので注意深く扱う。ここでは c_k(t) = a_k(t) - i b_k(t) としたため、
                // u(x,t) = Re(c_0) + Re(sum_{k=1}^N (c_k e^{ikx} + c_{-k} e^{-ikx}))
                //       = a_0 + sum_{k=1}^N (a_k cos(kx) + b_k sin(kx))  (ただし、係数の定義による)
                // 本解答では、c_k = (a_k - i b_k) としたので、実数のフーリエ級数表現では、
                // u(x,t) = a_0 + sum_{k=1}^{N_MODES} (a_k cos(kx) + b_k sin(kx))
                // となることに注意。

                u_real[i] += ak_n[k] * cos(k * x[i]) + bk_n[k] * sin(k * x[i]);
            }
        }
        
        // データのファイル出力
        sprintf(fname, "data/data_%04d.txt", n); // ゼロパディング
        fp_data = fopen(fname, "w");
        if (fp_data == NULL) {
            perror("Error opening data file");
            return 1;
        }
        for (i = 0; i <= NX; i++) {
            fprintf(fp_data, "%lf\t%lf\n", x[i], u_real[i]);
        }
        fclose(fp_data);

        // Gnuplotでプロット
        sprintf(fname, "anim/anim_%04d.png", n); // ゼロパディング
        fp_gnuplot = popen("gnuplot", "w");
        if (fp_gnuplot == NULL) {
            perror("Error opening gnuplot pipe");
            return 1;
        }
        fprintf(fp_gnuplot, "set terminal png\n");
        fprintf(fp_gnuplot, "set xrange [0:%lf]\n", 2.0 * M_PI); // ドメインの長さに合わせてレンジを設定
        // yrange は exp(x) の最大値 exp(2π) ~= 535 を考慮して設定
        fprintf(fp_gnuplot, "set yrange [0:600]\n"); // 適切な範囲に調整
        fprintf(fp_gnuplot, "set output '%s'\n", fname);
        fprintf(fp_gnuplot, "plot 'data/data_%04d.txt' w l title 'u(x,t)'\n", n);
        pclose(fp_gnuplot);

        // オイラー法で次のタイムステップのフーリエ係数を計算
        // k=0 の項は時間変化しない
        ak_np1[0] = ak_n[0];
        bk_np1[0] = bk_n[0];

        for (k = 1; k <= N_MODES; k++) {
            ak_np1[k] = ak_n[k] + dt * (C_VEL * k * bk_n[k]);
            bk_np1[k] = bk_n[k] + dt * (-C_VEL * k * ak_n[k]);
        }

        // 次のタイムステップのために現在の係数を更新
        for (k = 0; k <= N_MODES; k++) {
            ak_n[k] = ak_np1[k];
            bk_n[k] = bk_np1[k];
        }
    }

    return 0;
}